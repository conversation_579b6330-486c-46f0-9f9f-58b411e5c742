"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuth, useRequireAuth } from "@/hooks/use-auth";
import { authUtils } from "@/lib/auth-utils";
import { useRouter } from "next/navigation";

export default function DashboardPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  const { isAuthenticated } = useRequireAuth();

  const handleSignOut = async () => {
    try {
      await authUtils.signOut();
      router.push("/");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Contul meu</h1>
              <p className="text-muted-foreground mt-1">
                Bine ai venit, {user?.username || user?.name}!
              </p>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Deconectează-te
            </Button>
          </div>

          <div className="w-full">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Profilul tău</CardTitle>
                <CardDescription>Informații despre contul tău</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Email:</span> {user?.email}
                  </div>
                  {user?.username && (
                    <div>
                      <span className="font-medium">Username:</span>{" "}
                      {user?.username}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Nume:</span>{" "}
                    {user?.name || "Nu este setat"}
                  </div>
                  <div>
                    <span className="font-medium">Cont creat:</span>{" "}
                    {user?.createdAt
                      ? new Date(user.createdAt).toLocaleString("ro-RO")
                      : "N/A"}
                  </div>
                </div>
              </CardContent>
            </Card>

            <h2 className="text-3xl mt-8 text-center w-full">
              Pagina în curs de dezvoltare...
            </h2>
          </div>
        </div>
      </div>
    </div>
  );
}
