import { authClient } from "./auth-client";
import { SignUpFormData, SignInFormData } from "./auth-schemas";

export const authUtils = {
  // Sign up with email and username
  async signUp(data: Omit<SignUpFormData, "confirmPassword" | "acceptTerms">) {
    try {
      const result: any = await authClient.signUp.email({
        email: data.email,
        password: data.password,
        username: data.username,
        name: data.username, // Use username as display name initially
      });

      if (result.error) {
        throw new Error(result.error.message || "Eroare la înregistrare");
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Sign up error:", error);
      throw error;
    }
  },

  // Sign in with email or username
  async signIn(data: SignInFormData) {
    try {
      let result: any;

      // If identifier looks like email, try email sign-in first
      if (data.identifier.includes("@")) {
        result = await authClient.signIn.email({
          email: data.identifier,
          password: data.password,
          callbackURL: "/my-account",
        });

        // If email sign-in fails, try username sign-in
        if (result.error) {
          result = await authClient.signIn.username({
            username: data.identifier,
            password: data.password,
          });
        }
      } else {
        // Try username sign-in first
        result = await authClient.signIn.username({
          username: data.identifier,
          password: data.password,
        });

        // If username sign-in fails, try email sign-in
        if (result.error) {
          result = await authClient.signIn.email({
            email: data.identifier,
            password: data.password,
            callbackURL: "/my-account",
          });
        }
      }

      if (result.error) {
        throw new Error(
          "S-a produs o eroare la autentificare. Verificați datele introduse sau contactați suportul tehnic."
        );
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Sign in error:", error);
      throw error;
    }
  },

  // Sign in with Google
  async signInWithGoogle() {
    try {
      const result: any = await authClient.signIn.social({
        provider: "google",
      });

      if (result.error) {
        throw new Error(
          result.error.message || "Eroare la autentificare cu Google"
        );
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error("Google sign in error:", error);
      throw error;
    }
  },

  // Sign out
  async signOut() {
    try {
      const result: any = await authClient.signOut();
      if (result.error) {
        throw new Error(result.error.message || "Eroare la deconectare");
      }
      return { success: true };
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  },

  // Get current session
  async getSession() {
    try {
      const session = await authClient.getSession();
      return session;
    } catch (error) {
      console.error("Get session error:", error);
      return null;
    }
  },

  // Update user profile
  async updateUser(data: { username?: string; name?: string }) {
    try {
      const result: any = await authClient.updateUser(data);
      if (result.error) {
        throw new Error(result.error.message || "Eroare la actualizare profil");
      }
      return { success: true, data: result.data };
    } catch (error) {
      console.error("Update user error:", error);
      throw error;
    }
  },
};
